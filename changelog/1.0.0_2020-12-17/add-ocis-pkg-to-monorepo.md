Enhancement: Add the ocis-pkg package

Tags: ocis-pkg

* Change - Unwrap roleIDs from access-token into metadata context: [#59](https://github.com/owncloud/ocis-pkg/pull/59)
* Change - Provide cache for roles: [#59](https://github.com/owncloud/ocis-pkg/pull/59)
* Change - Roles manager: [#60](https://github.com/owncloud/ocis-pkg/pull/60)
* Change - Use go-micro's metadata context for account id: [#56](https://github.com/owncloud/ocis-pkg/pull/56)
* Bugfix - Remove redigo 2.0.0+incompatible dependency: [#33](https://github.com/owncloud/ocis-graph/pull/33)
* Change - Add middleware for x-access-token dismantling: [#46](https://github.com/owncloud/ocis-pkg/pull/46)
* Enhancement - Add `ocis.id` and numeric id claims: [#50](https://github.com/owncloud/ocis-pkg/pull/50)
* Bugfix - Pass flags to micro service: [#44](https://github.com/owncloud/ocis-pkg/pull/44)
* Change - Add header to cors handler: [#41](https://github.com/owncloud/ocis-pkg/issues/41)
* Enhancement - Tracing middleware: [#35](https://github.com/owncloud/ocis-pkg/pull/35/)
* Enhancement - Allow http services to register handlers: [#33](https://github.com/owncloud/ocis-pkg/pull/33)
* Change - Upgrade the micro libraries: [#22](https://github.com/owncloud/ocis-pkg/pull/22)
* Bugfix - Fix Module Path: [#25](https://github.com/owncloud/ocis-pkg/pull/25)
* Bugfix - Change import paths to ocis-pkg/v2: [#27](https://github.com/owncloud/ocis-pkg/pull/27)
* Bugfix - Fix serving static assets: [#14](https://github.com/owncloud/ocis-pkg/pull/14)
* Change - Add TLS support for http services: [#19](https://github.com/owncloud/ocis-pkg/issues/19)
* Enhancement - Introduce OpenID Connect middleware: [#8](https://github.com/owncloud/ocis-pkg/issues/8)
* Change - Add root path to static middleware: [#9](https://github.com/owncloud/ocis-pkg/issues/9)
* Change - Better log level handling within micro: [#2](https://github.com/owncloud/ocis-pkg/issues/2)

https://github.com/owncloud/product/issues/244
