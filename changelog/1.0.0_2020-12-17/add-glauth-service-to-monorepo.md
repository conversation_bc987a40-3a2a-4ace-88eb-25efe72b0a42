Enhancement: Add the glauth service

Tags: glauth

* Bugfix - Return invalid credentials when user was not found: [#30](https://github.com/owncloud/ocis-glauth/pull/30)
* Bugfix - Query numeric attribute values without quotes: [#28](https://github.com/owncloud/ocis-glauth/issues/28)
* Bugfix - Use searchBaseDN if already a user/group name: [#214](https://github.com/owncloud/product/issues/214)
* Bugfix - Fix LDAP substring startswith filters: [#31](https://github.com/owncloud/ocis-glauth/pull/31)
* Enhancement - Add build information to the metrics: [#226](https://github.com/owncloud/product/issues/226)
* Enhancement - Reenable configuring backends: [#600](https://github.com/owncloud/ocis/pull/600)
* Bugfix - Ignore case when comparing objectclass values: [#26](https://github.com/owncloud/ocis-glauth/pull/26)
* Bugfix - Build docker images with alpine:latest instead of alpine:edge: [#24](https://github.com/owncloud/ocis-glauth/pull/24)
* Enhancement - Handle ownCloudUUID attribute: [#27](https://github.com/owncloud/ocis-glauth/pull/27)
* Enhancement - Implement group queries: [#22](https://github.com/owncloud/ocis-glauth/issues/22)
* Enhancement - Configuration: [#11](https://github.com/owncloud/ocis-glauth/pull/11)
* Enhancement - Improve default settings: [#12](https://github.com/owncloud/ocis-glauth/pull/12)
* Enhancement - Generate temporary ldap certificates if LDAPS is enabled: [#12](https://github.com/owncloud/ocis-glauth/pull/12)
* Enhancement - Provide additional tls-endpoint: [#12](https://github.com/owncloud/ocis-glauth/pull/12)
* Change - Use physicist demo users: [#5](https://github.com/owncloud/ocis-glauth/issues/5)
* Change - Default to config based user backend: [#6](https://github.com/owncloud/ocis-glauth/pull/6)

https://github.com/owncloud/product/issues/244
