Enhancement: Add the ocs service

Tags: ocs

* Bugfix - Match the user response to the OC10 format: [#181](https://github.com/owncloud/product/issues/181)
* Enhancement - Add version command: [#226](https://github.com/owncloud/product/issues/226)
* Bugfix - Add the top level response structure to json responses: [#181](https://github.com/owncloud/product/issues/181)
* Enhancement - Update ocis-accounts: [#42](https://github.com/owncloud/ocis-ocs/pull/42)
* Bugfix - Mimic oc10 user enabled as string in provisioning api: [#39](https://github.com/owncloud/ocis-ocs/pull/39)
* Bugfix - Use opaque ID of a user for signing keys: [#436](https://github.com/owncloud/ocis/issues/436)
* Enhancement - Add option to create user with uidnumber and gidnumber: [#34](https://github.com/owncloud/ocis-ocs/pull/34)
* Bugfix - Fix file descriptor leak: [#79](https://github.com/owncloud/ocis-accounts/issues/79)
* Enhancement - Add Group management for OCS Provisioning API: [#25](https://github.com/owncloud/ocis-ocs/pull/25)
* Enhancement - Basic Support for the User Provisioning API: [#23](https://github.com/owncloud/ocis-ocs/pull/23)
* Bugfix - Build docker images with alpine:latest instead of alpine:edge: [#20](https://github.com/owncloud/ocis-ocs/pull/20)
* Change - Initial release of basic version: [#1](https://github.com/owncloud/ocis-ocs/issues/1)
* Change - Upgrade micro libraries: [#11](https://github.com/owncloud/ocis-ocs/issues/11)
* Enhancement - Configuration: [#14](https://github.com/owncloud/ocis-ocs/pull/14)
* Enhancement - Support signing key: [#18](https://github.com/owncloud/ocis-ocs/pull/18)

https://github.com/owncloud/product/issues/244
