---
_extends: gh-labels

repository:
  name: ocis
  description: ':atom_symbol: ownCloud Infinite Scale Stack'
  homepage: 'https://doc.owncloud.com/ocis/next/'
  topics: reva, ocis

  private: false
  has_issues: true
  has_projects: true
  has_wiki: false
  has_downloads: false

  default_branch: master

  allow_squash_merge: true
  allow_merge_commit: true
  allow_rebase_merge: true

labels:
  - name: OCIS-Fastlane
    color: "#deadbf"
    description: Planned outside of the sprint
  - name: Storage:EOS
    color: "#3F7A62"
  - name: Storage:S3NG
    color: "#3F7A62"
  - name: Storage:CephFS
    color: "#3F7A62"
  - name: Storage:OCIS
    color: "#3F7A62"
  - name: Storage:POSIX
    color: "#3F7A62"
  - name: Storage:ownCloudSQL
    color: "#3F7A62"

teams:
  - name: ci
    permission: admin
  - name: employees
    permission: push
  - name: cern
    permission: triage
  - name: ocis-contractors
    permission: push

branches:
  - name: master
    protection:
      required_pull_request_reviews:
        required_approving_review_count: 1
        dismiss_stale_reviews: false
        require_code_owner_reviews: false
      required_status_checks:
        strict: false
        contexts:
          - continuous-integration/drone/pr
      enforce_admins: null
      restrictions:
        apps: []
        users:
          - dependabot
        teams:
          - ci
          - employees
          - ocis-contractors
  - name: stable-*
    protection:
      required_pull_request_reviews:
        required_approving_review_count: 2
        dismiss_stale_reviews: false
        require_code_owner_reviews: false
      required_status_checks:
        strict: false
        contexts:
          - continuous-integration/drone/pr
      enforce_admins: null
      restrictions:
        apps: []
        users:
          - dependabot
        teams:
          - ci
          - employees
          - ocis-contractors

...

