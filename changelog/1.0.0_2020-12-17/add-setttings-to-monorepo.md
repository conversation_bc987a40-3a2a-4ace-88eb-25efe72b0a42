Enhancement: Add the settings service

Tags: settings

* Bugfix - Fix loading and saving system scoped values: [#66](https://github.com/owncloud/ocis-settings/pull/66)
* Bugfix - Complete input validation: [#66](https://github.com/owncloud/ocis-settings/pull/66)
* Change - Add filter option for bundle ids in ListBundles and ListRoles: [#59](https://github.com/owncloud/ocis-settings/pull/59)
* Change - Reuse roleIDs from the metadata context: [#69](https://github.com/owncloud/ocis-settings/pull/69)
* Change - Update ocis-pkg/v2: [#72](https://github.com/owncloud/ocis-settings/pull/72)
* Enhancement - Add version command: [#226](https://github.com/owncloud/product/issues/226)
* Bugfix - Fix fetching bundles in settings UI: [#61](https://github.com/owncloud/ocis-settings/pull/61)
* Change - Filter settings by permissions: [#99](https://github.com/owncloud/product/issues/99)
* Change - Add role service: [#110](https://github.com/owncloud/product/issues/110)
* Change - Rename endpoints and message types: [#36](https://github.com/owncloud/ocis-settings/issues/36)
* Change - Use UUIDs instead of alphanumeric identifiers: [#46](https://github.com/owncloud/ocis-settings/pull/46)
* Bugfix - Adjust UUID validation to be more tolerant: [#41](https://github.com/owncloud/ocis-settings/issues/41)
* Bugfix - Fix runtime error when type asserting on nil value: [#38](https://github.com/owncloud/ocis-settings/pull/38)
* Bugfix - Fix multiple submits on string and number form elements: [#745](https://github.com/owncloud/owncloud-design-system/issues/745)
* Bugfix - Build docker images with alpine:latest instead of alpine:edge: [#39](https://github.com/owncloud/ocis-settings/pull/39)
* Change - Dynamically add navItems for extensions with settings bundles: [#25](https://github.com/owncloud/ocis-settings/pull/25)
* Change - Introduce input validation: [#22](https://github.com/owncloud/ocis-settings/pull/22)
* Change - Use account uuid from x-access-token: [#14](https://github.com/owncloud/ocis-settings/pull/14)
* Change - Use server config variable from ocis-web: [#34](https://github.com/owncloud/ocis-settings/pull/34)
* Enhancement - Remove paths from Makefile: [#33](https://github.com/owncloud/ocis-settings/pull/33)
* Enhancement - Extend the docs: [#11](https://github.com/owncloud/ocis-settings/issues/11)
* Enhancement - Update ocis-pkg/v2: [#42](https://github.com/owncloud/ocis-settings/pull/42)

https://github.com/owncloud/product/issues/244
