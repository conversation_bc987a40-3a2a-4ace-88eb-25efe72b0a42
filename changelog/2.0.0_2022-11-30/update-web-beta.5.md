Enhancement: Update ownCloud Web to v5.7.0-rc.4

Tags: web

We updated ownCloud Web to v5.7.0-rc.4. Please refer to the changelog (linked) for details on the web release.

* Bugfix [owncloud/web#7230](https://github.com/owncloud/web/pull/7230): Context menu misplaced when triggered by keyboard navigation
* Bugfix [owncloud/web#7214](https://github.com/owncloud/web/pull/7214): Prevent error when pasting with empty clipboard
* Bugfix [owncloud/web#7173](https://github.com/owncloud/web/pull/7173): Re-introduce dynamic app name in document title
* Bugfix [owncloud/web#7166](https://github.com/owncloud/web/pull/7166): External apps fixes
* Bugfix [owncloud/web#7248](https://github.com/owncloud/web/pull/7248): Hide empty trash bin modal on error
* Bugfix [owncloud/web#4677](https://github.com/owncloud/web/issues/4677): Logout deleted user on page reload
* Bugfix [owncloud/web#7216](https://github.com/owncloud/web/pull/7216): Filename hovers over the image in the preview app
* Bugfix [owncloud/web#7228](https://github.com/owncloud/web/pull/7228): Shared with others page apps not working with oc10 as backend
* Bugfix [owncloud/web#7197](https://github.com/owncloud/web/pull/7197): Create space and access user management permission
* Bugfix [owncloud/web#6921](https://github.com/owncloud/web/pull/6921): Space sidebar sharing indicators
* Bugfix [owncloud/web#7030](https://github.com/owncloud/web/issues/7030): Access token renewal
* Enhancement [owncloud/web#7217](https://github.com/owncloud/web/pull/7217): Add app top bar component
* Enhancement [owncloud/web#7153](https://github.com/owncloud/web/pull/7153): Add Keyboard navigation/selection
* Enhancement [owncloud/web#7030](https://github.com/owncloud/web/issues/7030): Loading context blocks application bootstrap
* Enhancement [owncloud/web#7206](https://github.com/owncloud/web/pull/7206): Add change own password dialog to the account info page
* Enhancement [owncloud/web#7086](https://github.com/owncloud/web/pull/7086): Re-sharing for ocis
* Enhancement [owncloud/web#7201](https://github.com/owncloud/web/pull/7201): Added a toolbar to pdf-viewer app
* Enhancement [owncloud/web#7139](https://github.com/owncloud/web/pull/7139): Reposition notifications
* Enhancement [owncloud/web#7030](https://github.com/owncloud/web/issues/7030): Resolve bookmarked public links with password protection
* Enhancement [owncloud/web#7038](https://github.com/owncloud/web/issues/7038): Improve performance of share indicators
* Enhancement [owncloud/web#6661](https://github.com/owncloud/web/issues/6661): Option to block file extensions from text-editor app
* Enhancement [owncloud/web#7139](https://github.com/owncloud/web/pull/7139): Update ODS to v14.0.0-alpha.4
* Enhancement [owncloud/web#7176](https://github.com/owncloud/web/pull/7176): Introduce group assignments

https://github.com/owncloud/ocis/pull/4140
https://github.com/owncloud/web/releases/tag/v5.7.0-rc.4
