Enhancement: Update ownCloud Web to v5.5.0-rc.9

Tags: web

We updated ownCloud Web to v5.5.0-rc.9. Please refer to the changelog (linked) for details on the web release.

Summary
-------

* Bugfix [owncloud/web#6939](https://github.com/owncloud/web/pull/6939): Not logged out if backend is ownCloud 10
* Bugfix [owncloud/web#7061](https://github.com/owncloud/web/pull/7061): Prevent rename button from getting covered
* Bugfix [owncloud/web#7032](https://github.com/owncloud/web/pull/7032): Show message when upload size exceeds quota
* Bugfix [owncloud/web#7036](https://github.com/owncloud/web/pull/7036): Drag and drop upload when a file is selected
* Enhancement [owncloud/web#7022](https://github.com/owncloud/web/pull/7022): Add config option for hoverable quick actions
* Enhancement [owncloud/web#6555](https://github.com/owncloud/web/issues/6555): Consistent dropdown menus
* Enhancement [owncloud/web#6994](https://github.com/owncloud/web/pull/6994): Copy/Move conflict dialog
* Enhancement [owncloud/web#6750](https://github.com/owncloud/web/pull/6750): Make contexthelpers opt-out
* Enhancement [owncloud/web#7038](https://github.com/owncloud/web/issues/7038): Rendering of share-indicators in ResourceTable
* Enhancement [owncloud/web#6776](https://github.com/owncloud/web/issues/6776): Prevent the resource name in the sidebar from being truncated
* Enhancement [owncloud/web#7067](https://github.com/owncloud/web/pull/7067): Upload progress & overlay improvements

https://github.com/owncloud/web/pull/6854
https://github.com/owncloud/ocis/pull/3927
https://github.com/owncloud/web/releases/tag/v5.5.0-rc.9
