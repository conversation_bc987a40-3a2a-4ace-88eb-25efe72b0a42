Enhancement: Update ownCloud Web to v5.7.0

Tags: web

We updated ownCloud Web to v5.7.0. Please refer to the changelog (linked) for details on the web release.

* Bugfix [owncloud/web#7522](https://github.com/owncloud/web/pull/7522): Allow uploads outside of user's home despite quota being exceeded
* Bugfix [owncloud/web#7622](https://github.com/owncloud/web/issues/7622): Expiration date picker with long language codes
* Bugfix [owncloud/web#7516](https://github.com/owncloud/web/pull/7516): File name in text editor
* Bugfix [owncloud/web#7498](https://github.com/owncloud/web/issues/7498): Fix right sidebar content on small screens
* Bugfix [owncloud/web#7455](https://github.com/owncloud/web/issues/7455): Improve keyboard shortcuts copy/cut files
* Bugfix [owncloud/web#7510](https://github.com/owncloud/web/issues/7510): Paste action (keyboard) not working in project spaces
* Bugfix [owncloud/web#7526](https://github.com/owncloud/web/issues/7526): Left sidebar when switching apps
* Bugfix [owncloud/web#7582](https://github.com/owncloud/web/issues/7582): Merge share with group and group member into one
* Bugfix [owncloud/web#7534](https://github.com/owncloud/web/issues/7534): Redirect after removing self from space members
* Bugfix [owncloud/web#7560](https://github.com/owncloud/web/pull/7560): Search share representation
* Bugfix [owncloud/web#7519](https://github.com/owncloud/web/issues/7519): Sidebar for current folder
* Bugfix [owncloud/web#7453](https://github.com/owncloud/web/issues/7453): Stuck After Session Expired
* Bugfix [owncloud/web#7595](https://github.com/owncloud/web/pull/7595): Typo when reading public links capabilities
* Enhancement [owncloud/web#7570](https://github.com/owncloud/web/pull/7570): Adjust spacing of the files list options menu
* Enhancement [owncloud/web#7540](https://github.com/owncloud/web/issues/7540): Left sidebar hover effect
* Enhancement [owncloud/web#7555](https://github.com/owncloud/web/pull/7555): Propose unique file name while creating a new file
* Enhancement [owncloud/web#7038](https://github.com/owncloud/web/issues/7038): Reduce pagination options
* Enhancement [owncloud/web#6173](https://github.com/owncloud/web/pull/6173): Remember the UI that was last selected via the application switcher
* Enhancement [owncloud/web#7584](https://github.com/owncloud/web/pull/7584): Remove clickOutside directive
* Enhancement [owncloud/web#7485](https://github.com/owncloud/web/pull/7485): Add resource name to the WebDAV properties
* Enhancement [owncloud/web#7559](https://github.com/owncloud/web/pull/7559): Don't open right sidebar from private links
* Enhancement [owncloud/web#7586](https://github.com/owncloud/web/pull/7586): Search improvements
* Enhancement [owncloud/web#7605](https://github.com/owncloud/web/pull/7605): Simplify mime type checking
* Enhancement [owncloud/web#7626](https://github.com/owncloud/web/pull/7626): Update ODS to v14.0.0-alpha.18
* Enhancement [owncloud/web#7177](https://github.com/owncloud/web/issues/7177): Update Uppy to v3.0.1
* Enhancement [owncloud/web#7182](https://github.com/owncloud/web/pull/7182): User management app edit quota

https://github.com/owncloud/ocis/pull/4508
https://github.com/owncloud/ocis/pull/4547
https://github.com/owncloud/ocis/pull/4550
https://github.com/owncloud/web/releases/tag/v5.7.0
