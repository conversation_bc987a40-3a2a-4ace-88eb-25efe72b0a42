Enhancement: Update reva to v1.4.1-0.20201123062044-b2c4af4e897d

* Refactor the uploading files workflow from various clients [cs3org/reva#1285](https://github.com/cs3org/reva/pull/1285), [cs3org/reva#1314](https://github.com/cs3org/reva/pull/1314)
* [OCS] filter share with me requests [cs3org/reva#1302](https://github.com/cs3org/reva/pull/1302)
* Fix listing shares for nonexistent path [cs3org/reva#1316](https://github.com/cs3org/reva/pull/1316)
* prevent nil pointer when listing shares [cs3org/reva#1317](https://github.com/cs3org/reva/pull/1317)
* Share<PERSON> retrieves the information about a share -but gets response containing all the shares [owncloud/ocis-reva#260](https://github.com/owncloud/ocis-reva/issues/260)
* Deleting a public link after renaming a file [owncloud/ocis-reva#311](https://github.com/owncloud/ocis-reva/issues/311)
* Avoid log spam [cs3org/reva#1323](https://github.com/cs3org/reva/pull/1323), [cs3org/reva#1324](https://github.com/cs3org/reva/pull/1324)
* Fix trashbin [cs3org/reva#1326](https://github.com/cs3org/reva/pull/1326)

https://github.com/owncloud/ocis/pull/823
https://github.com/cs3org/reva/pull/1285
https://github.com/cs3org/reva/pull/1302
https://github.com/cs3org/reva/pull/1314
https://github.com/cs3org/reva/pull/1316
https://github.com/cs3org/reva/pull/1317
https://github.com/cs3org/reva/pull/1323
https://github.com/cs3org/reva/pull/1324
https://github.com/cs3org/reva/pull/1326
https://github.com/owncloud/ocis-reva/issues/260
https://github.com/owncloud/ocis-reva/issues/311
