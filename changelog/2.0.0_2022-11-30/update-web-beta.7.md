Enhancement: Update ownCloud Web to v5.7.0-rc.10

Tags: web

We updated ownCloud Web to v5.7.0-rc.10. Please refer to the changelog (linked) for details on the web release.

* Bugfix [owncloud/web#7443](https://github.com/owncloud/web/pull/7443): Datetime formatting
* Bugfix [owncloud/web#7437](https://github.com/owncloud/web/pull/7437): Default to user context
* Bugfix [owncloud/web#7473](https://github.com/owncloud/web/pull/7473): Dragging a file causes no selection
* Bugfix [owncloud/web#7469](https://github.com/owncloud/web/pull/7469): File size not updated while restoring file version
* Bugfix [owncloud/web#7443](https://github.com/owncloud/web/pull/7443): File size formatting
* Bugfix [owncloud/web#7474](https://github.com/owncloud/web/pull/7474): Load only supported thumbnails (configurable)
* Bugfix [owncloud/web#7309](https://github.com/owncloud/web/pull/7309): SidebarNavItem icon flickering
* Bugfix [owncloud/web#7425](https://github.com/owncloud/web/pull/7425): Open Folder in project space context menu
* Bugfix [owncloud/web#7486](https://github.com/owncloud/web/issues/7486): Prevent unnecessary PROPFIND request during upload
* Bugfix [owncloud/web#7415](https://github.com/owncloud/web/pull/7415): Re-fetch quota
* Bugfix [owncloud/web#7478](https://github.com/owncloud/web/issues/7478): "Shared via"-indicator for links
* Bugfix [owncloud/web#7480](https://github.com/owncloud/web/issues/7480): Missing space image in sidebar
* Bugfix [owncloud/web#7436](https://github.com/owncloud/web/issues/7436): Hide share actions for space viewers/editors
* Bugfix [owncloud/web#7445](https://github.com/owncloud/web/pull/7445): User management app close side bar throws error
* Enhancement [owncloud/web#7309](https://github.com/owncloud/web/pull/7309): Keyboard shortcut indicators in ContextMenu
* Enhancement [owncloud/web#7309](https://github.com/owncloud/web/pull/7309): Lowlight cut resources
* Enhancement [owncloud/web#7133](https://github.com/owncloud/web/pull/7133): Permissionless (internal) link shares
* Enhancement [owncloud/web#7309](https://github.com/owncloud/web/pull/7309): Replace locationpicker with clipboard actions
* Enhancement [owncloud/web#7363](https://github.com/owncloud/web/pull/7363): Streamline UI sizings
* Enhancement [owncloud/web#7355](https://github.com/owncloud/web/pull/7355): Update ODS to v14.0.0-alpha.16
* Enhancement [owncloud/web#7476](https://github.com/owncloud/web/pull/7476): Users table on small screen
* Enhancement [owncloud/web#7182](https://github.com/owncloud/web/pull/7182): User management app edit quota

https://github.com/owncloud/ocis/pull/4439
https://github.com/owncloud/web/releases/tag/v5.7.0-rc.10
