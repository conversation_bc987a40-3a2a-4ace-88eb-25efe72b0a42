Enhancement: Add the accounts service

Tags: accounts

* Bugfix - Initialize roleService client in GRPC server: [#114](https://github.com/owncloud/ocis-accounts/pull/114)
* Bugfix - Cleanup separated indices in memory: [#224](https://github.com/owncloud/product/issues/224)
* Change - Set user role on builtin users: [#102](https://github.com/owncloud/ocis-accounts/pull/102)
* Change - Add new builtin admin user: [#102](https://github.com/owncloud/ocis-accounts/pull/102)
* Change - We make use of the roles cache to enforce permission checks: [#100](https://github.com/owncloud/ocis-accounts/pull/100)
* Change - We make use of the roles manager to enforce permission checks: [#108](https://github.com/owncloud/ocis-accounts/pull/108)
* Enhancement - Add create account form: [#148](https://github.com/owncloud/product/issues/148)
* Enhancement - Add delete accounts action: [#148](https://github.com/owncloud/product/issues/148)
* Enhancement - Add enable/disable capabilities to the WebUI: [#118](https://github.com/owncloud/product/issues/118)
* Enhancement - Improve visual appearance of accounts UI: [#222](https://github.com/owncloud/product/issues/222)
* Bugfix - Adapting to new settings API for fetching roles: [#96](https://github.com/owncloud/ocis-accounts/pull/96)
* Change - Create account api-call implicitly adds "default-user" role: [#173](https://github.com/owncloud/product/issues/173)
* Change - Add role selection to accounts UI: [#103](https://github.com/owncloud/product/issues/103)
* Bugfix - Atomic Requests: [#82](https://github.com/owncloud/ocis-accounts/pull/82)
* Bugfix - Unescape value for prefix query: [#76](https://github.com/owncloud/ocis-accounts/pull/76)
* Change - Adapt to new ocis-settings data model: [#87](https://github.com/owncloud/ocis-accounts/pull/87)
* Change - Add permissions for language to default roles: [#88](https://github.com/owncloud/ocis-accounts/pull/88)
* Bugfix - Add write mutexes: [#71](https://github.com/owncloud/ocis-accounts/pull/71)
* Bugfix - Fix the accountId and groupId mismatch in DeleteGroup Method: [#60](https://github.com/owncloud/ocis-accounts/pull/60)
* Bugfix - Fix index mapping: [#73](https://github.com/owncloud/ocis-accounts/issues/73)
* Bugfix - Use NewNumericRangeInclusiveQuery for numeric literals: [#28](https://github.com/owncloud/ocis-glauth/issues/28)
* Bugfix - Prevent segfault when no password is set: [#65](https://github.com/owncloud/ocis-accounts/pull/65)
* Bugfix - Update account return value not used: [#70](https://github.com/owncloud/ocis-accounts/pull/70)
* Bugfix - Build docker images with alpine:latest instead of alpine:edge: [#64](https://github.com/owncloud/ocis-accounts/pull/64)
* Change - Align structure of this extension with other extensions: [#51](https://github.com/owncloud/ocis-accounts/pull/51)
* Change - Change api errors: [#11](https://github.com/owncloud/ocis-accounts/issues/11)
* Change - Enable accounts on creation: [#43](https://github.com/owncloud/ocis-accounts/issues/43)
* Change - Fix index update on create/update: [#57](https://github.com/owncloud/ocis-accounts/issues/57)
* Change - Pass around the correct logger throughout the code: [#41](https://github.com/owncloud/ocis-accounts/issues/41)
* Change - Remove timezone setting: [#33](https://github.com/owncloud/ocis-accounts/pull/33)
* Change - Tighten screws on usernames and email addresses: [#65](https://github.com/owncloud/ocis-accounts/pull/65)
* Enhancement - Add early version of cli tools for user-management: [#69](https://github.com/owncloud/ocis-accounts/pull/69)
* Enhancement - Update accounts API: [#30](https://github.com/owncloud/ocis-accounts/pull/30)
* Enhancement - Add simple user listing UI: [#51](https://github.com/owncloud/ocis-accounts/pull/51)
* Enhancement - Logging is configurable: [#24](https://github.com/owncloud/ocis-accounts/pull/24)
* Change - Initial release of basic version: [#1](https://github.com/owncloud/ocis-accounts/issues/1)
* Enhancement - Configuration: [#15](https://github.com/owncloud/ocis-accounts/pull/15)

https://github.com/owncloud/product/issues/244
