Bugfix: Remove unused configuration options

We've removed multiple unused configuration options:

- `STORAGE_SYSTEM_DATAPROVIDER_INSECURE`, see also cs3org/reva#2993
- `STORAGE_USERS_DATAPROVIDER_INSECURE`, see also cs3org/reva#2993
- `STORAGE_SYSTEM_TEMP_FOLDER`, see also cs3org/reva#2993
- `STORAGE_USERS_TEMP_FOLDER`, see also cs3org/reva#2993
- `WEB_UI_CONFIG_VERSION`, see also owncloud/web#7130
- `GATEWAY_COMMIT_SHARE_TO_STORAGE_REF`, see also cs3org/reva#3017

https://github.com/owncloud/ocis/pull/3973
