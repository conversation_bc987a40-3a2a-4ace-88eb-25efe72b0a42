Enhancement: Add the ocis-phoenix service

Tags: web

* Bugfix - Fix external app URLs: [#218](https://github.com/owncloud/product/issues/218)
* Change - Remove pdf-viewer from default apps: [#85](https://github.com/owncloud/ocis-phoenix/pull/85)
* Change - Enable Settings and Accounts apps by default: [#80](https://github.com/owncloud/ocis-phoenix/pull/80)
* Bugfix - Exit when assets or config are not found: [#76](https://github.com/owncloud/ocis-phoenix/pull/76)
* Bugfix - Build docker images with alpine:latest instead of alpine:edge: [#73](https://github.com/owncloud/ocis-phoenix/pull/73)
* Change - Hide searchbar by default: [#116](https://github.com/owncloud/product/issues/116)
* Bugfix - Allow silent refresh of access token: [#69](https://github.com/owncloud/ocis-konnectd/issues/69)
* Change - Update Phoenix: [#60](https://github.com/owncloud/ocis-phoenix/pull/60)
* Enhancement - Configuration: [#57](https://github.com/owncloud/ocis-phoenix/pull/57)
* Bugfix - Config file value not being read: [#45](https://github.com/owncloud/ocis-phoenix/pull/45)
* Change - Default to running behind ocis-proxy: [#55](https://github.com/owncloud/ocis-phoenix/pull/55)

https://github.com/owncloud/product/issues/244
