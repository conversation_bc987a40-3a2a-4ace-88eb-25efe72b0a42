Bugfix: Fix accounts initialization

Originally the accounts service relies on both the `settings` and `storage-metadata` to be up and running at the moment it starts. This is an antipattern as it will cause the entire service to panic if the dependants are not present.

We inverted this dependency and moved the default initialization data (i.e: creating roles, permissions, settings bundles) and instead of notifying the settings service that the account has to provide with such options, the settings is instead initialized with the options the accounts rely on. Essentially saving bandwidth as there is no longer a gRPC call to the settings service.

For the `storage-metadata` a retry mechanism was added that retries by default 20 times to fetch the `com.owncloud.storage.metadata` from the service registry every `500` milliseconds. If this retry expires the accounts panics, as its dependency on the `storage-metadata` service cannot be resolved.

We also introduced a client wrapper that acts as middleware between a client and a server. For more information on how it works further read [here](https://github.com/sony/gobreaker)

https://github.com/owncloud/ocis/pull/1696
