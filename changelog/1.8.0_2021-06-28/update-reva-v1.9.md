Enhancement: update RE<PERSON> to v1.9

This update includes
* [set Content-Type correctly](https://github.com/cs3org/reva/pull/1750)
* [Return file checksum available from the metadata for the EOS driver](https://github.com/cs3org/reva/pull/1755)
* [Sort share entries alphabetically](https://github.com/cs3org/reva/pull/1772)
* [Initial work on the owncloudsql driver](https://github.com/cs3org/reva/pull/1710)
* [Add user ID cache warmup to EOS storage driver](https://github.com/cs3org/reva/pull/1774)
* [Use UidNumber and GidNumber fields in User objects](https://github.com/cs3org/reva/pull/1573)
* [EOS GRPC interface](https://github.com/cs3org/reva/pull/1471)
* [switch references](https://github.com/cs3org/reva/pull/1721)
* [remove user's uuid from trashbin file key](https://github.com/cs3org/reva/pull/1793)
* [fix restore behavior of the trashbin API](https://github.com/cs3org/reva/pull/1795)
* [eosfs: add arbitrary metadata support](https://github.com/cs3org/reva/pull/1811)

https://github.com/owncloud/ocis/pull/2205
https://github.com/owncloud/ocis/pull/2210

