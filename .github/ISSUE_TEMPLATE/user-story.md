---
name: User story
about: Suggest an idea the agile way :-)
title: ''
labels: Type:Story
assignees: ''

---

# Description

## User Stories

* > As a ..., I want to ... so that ... (please stick to who, what, why)

## Value

## Acceptance Criteria

## Definition of ready
- [ ] Everybody needs to understand the value written in the user story
- [ ] Acceptance criteria have to be defined
- [ ] All dependencies of the user story need to be identified
- [ ] Feature should be seen from an end user perspective
- [ ] Story has to be estimated
- [ ] Story points need to be less than 20

## Definition of done
- Functional requirements
  - [ ] Functionality described in the user story works
  - [ ] Acceptance criteria are fulfilled
- Quality
  - [ ] Code review happened
  - [ ] CI is green (that includes new and existing automated tests)
  - [ ] Critical code received unit tests by the developer
- Non-functional requirements
  - [ ] No sonar cloud issues
- Configuration changes
  - [ ] The next branch of the ocis charts is compatible
