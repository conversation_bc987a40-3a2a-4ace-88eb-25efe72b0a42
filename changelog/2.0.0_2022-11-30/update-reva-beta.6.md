Enhancement: update reva to v2.7.4

Updated reva to version 2.7.4 This update includes:

*  Bugfix [cs3org/reva#3141](https://github.com/cs3org/reva/pull/3141): Check ListGrants permission when listing shares

Updated reva to version 2.7.3 This update includes:

*  Bugfix [cs3org/reva#3109](https://github.com/cs3org/reva/pull/3109): Bugfix missing check in MustCheckNodePermissions
*  Bugfix [cs3org/reva#3086](https://github.com/cs3org/reva/pull/3086): Bugfix crash in ldap authprovider
*  Bugfix [cs3org/reva#3094](https://github.com/cs3org/reva/pull/3094): Allow removing password from public links
*  Bugfix [cs3org/reva#3096](https://github.com/cs3org/reva/pull/3096): Bugfix user filter
*  Bugfix [cs3org/reva#3091](https://github.com/cs3org/reva/pull/3091): Project spaces need no real owner
*  Bugfix [cs3org/reva#3088](https://github.com/cs3org/reva/pull/3088): Use correct sublogger
*  Enhancement [cs3org/reva#3123](https://github.com/cs3org/reva/pull/3123): Allow stating links that have no permissions
*  Enhancement [cs3org/reva#3087](https://github.com/cs3org/reva/pull/3087): Allow to set LDAP substring filter type
*  Enhancement [cs3org/reva#3098](https://github.com/cs3org/reva/pull/3098): App provider http endpoint uses Form instead of Query
*  Enhancement [cs3org/reva#3133](https://github.com/cs3org/reva/pull/3133): Admins can set quota on all spaces
*  Enhancement [cs3org/reva#3117](https://github.com/cs3org/reva/pull/3117): Update go-ldap to v3.4.4
*  Enhancement [cs3org/reva#3095](https://github.com/cs3org/reva/pull/3095): Upload expiration and cleanup

https://github.com/owncloud/ocis/pull/4272
https://github.com/cs3org/reva/pull/3096
https://github.com/cs3org/reva/pull/4315

https://github.com/owncloud/ocis/pull/4294
https://github.com/owncloud/ocis/pull/4330
https://github.com/owncloud/ocis/pull/4369
