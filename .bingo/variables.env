# Auto generated binary variables helper managed by https://github.com/bwplotka/bingo v0.9. DO NOT EDIT.
# All tools are designed to be build inside $GOBIN.
# Those variables will work only until 'bingo get' was invoked, or if tools were installed via Makefile's Variables.mk.
GOBIN=${GOBIN:=$(go env GOBIN)}

if [ -z "$GOBIN" ]; then
	GOBIN="$(go env GOPATH)/bin"
fi


BINGO="${GOBIN}/bingo-v0.9.0"

BUF="${GOBIN}/buf-v1.39.0"

BUILDIFIER="${GOBIN}/buildifier-v0.0.0-20220323134444-a9f46b2bb3de"

CALENS="${GOBIN}/calens-v0.4.0"

GO_LICENSES="${GOBIN}/go-licenses-v1.5.0"

GOLANGCI_LINT="${GOBIN}/golangci-lint-v1.64.6"

GOVULNCHECK="${GOBIN}/govulncheck-v1.0.1"


MOCKERY="${GOBIN}/mockery-v2.52.3"

 

PIGEON="${GOBIN}/pigeon-v1.2.1"

PROTOC_GEN_DOC="${GOBIN}/protoc-gen-doc-v1.5.1"

PROTOC_GEN_GO="${GOBIN}/protoc-gen-go-v1.28.1"

PROTOC_GEN_MICRO="${GOBIN}/protoc-gen-micro-v1.0.0"

PROTOC_GEN_MICROWEB="${GOBIN}/protoc-gen-microweb-v0.0.0-20250812083720-c9765347567d"

PROTOC_GEN_OPENAPIV2="${GOBIN}/protoc-gen-openapiv2-v2.13.0"

PROTOC_GO_INJECT_TAG="${GOBIN}/protoc-go-inject-tag-v1.4.0"

REFLEX="${GOBIN}/reflex-v0.3.1"

