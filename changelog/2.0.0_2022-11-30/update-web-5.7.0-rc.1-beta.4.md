Enhancement: Update ownCloud Web to v5.7.0-rc.1

Tags: web

We updated ownCloud Web to v5.7.0-rc.1. Please refer to the changelog (linked) for details on the web release.

* Enhancement [owncloud/web#7119](https://github.com/owncloud/web/pull/7119): Copy/Move conflict dialog
* Enhancement [owncloud/web#7122](https://github.com/owncloud/web/pull/7122): Enable Drag&Drop and keyboard shortcuts for all views
* Enhancement [owncloud/web#7053](https://github.com/owncloud/web/pull/7053): Personal space id in URL
* Enhancement [owncloud/web#6933](https://github.com/owncloud/web/pull/6933): Customize additional mimeTypes for preview app
* Enhancement [owncloud/web#7078](https://github.com/owncloud/web/pull/7078): Add Hotkeys to ResourceTable
* Enhancement [owncloud/web#7120](https://github.com/owncloud/web/pull/7120): Use tus chunksize from backend
* Enhancement [owncloud/web#6749](https://github.com/owncloud/web/pull/6749): Update ODS to v13.2.0-rc.1
* Enhancement [owncloud/web#7111](https://github.com/owncloud/web/pull/7111): Upload data during creation
* Enhancement [owncloud/web#7109](https://github.com/owncloud/web/pull/7109): Clickable folder links in upload overlay
* Enhancement [owncloud/web#7123](https://github.com/owncloud/web/pull/7123): Indeterminate progress bar in upload overlay
* Enhancement [owncloud/web#7088](https://github.com/owncloud/web/pull/7088): Upload time estimation
* Enhancement [owncloud/web#7125](https://github.com/owncloud/web/pull/7125): Wording improvements
* Enhancement [owncloud/web#7140](https://github.com/owncloud/web/pull/7140): Separate direct and indirect link shares in sidebar
* Bugfix [owncloud/web#7156](https://github.com/owncloud/web/pull/7156): Folder link targets
* Bugfix [owncloud/web#7108](https://github.com/owncloud/web/pull/7108): Reload of an updated space-image and/or -readme
* Bugfix [owncloud/web#6846](https://github.com/owncloud/web/pull/6846): Upload meta data serialization
* Bugfix [owncloud/web#7100](https://github.com/owncloud/web/pull/7100): Complete-state of the upload overlay
* Bugfix [owncloud/web#7104](https://github.com/owncloud/web/pull/7104): Parent folder name on public links
* Bugfix [owncloud/web#7173](https://github.com/owncloud/web/pull/7173): Re-introduce dynamic app name in document title
* Bugfix [owncloud/web#7166](https://github.com/owncloud/web/pull/7166): External apps fixes

https://github.com/owncloud/ocis/pull/4005
https://github.com/owncloud/web/pull/7158
https://github.com/owncloud/ocis/pull/3990
https://github.com/owncloud/web/pull/6854
https://github.com/owncloud/web/releases/tag/v5.7.0-rc.1
