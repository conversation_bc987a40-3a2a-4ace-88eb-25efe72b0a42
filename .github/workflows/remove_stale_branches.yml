name: Remove stale branches
on:
  workflow_dispatch:
    inputs:
      dry-run:
        description: 'Run in dry-run mode (no actual branch deletion)'
        required: true
        default: 'false'
        type: boolean
  schedule:
    - cron: "0 0 * * *" # Everday at midnight
permissions: write-all

jobs:
  remove-stale-branches:
    name: Remove Stale Branches
    runs-on: ubuntu-latest
    steps:
      - uses: fpicalausa/remove-stale-branches@v1.6.0
        with:
          ignore-unknown-authors: true
          default-recipient: "kobergj"
          dry-run: ${{ inputs.dry-run || 'false' }} # fallback to dry-run
