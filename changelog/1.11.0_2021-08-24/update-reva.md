Enhancement: Update reva to v1.12

* Enhancement cs3org/reva#1803: Introduce new webdav spaces endpoint
* Bugfix cs3org/reva#1819: Disable notifications
* Enhancement cs3org/reva#1861: Add support for runtime plugins
* Bugfix cs3org/reva#1913: Logic to restore files to readonly nodes
* Enhancement cs3org/reva#1946: Add share manager that connects to oc10 databases
* Bugfix cs3org/reva#1954: Fix response format of the sharees API
* Bugfix cs3org/reva#1956: Fix trashbin listing with depth 0
* Bugfix cs3org/reva#1957: Fix etag propagation on deletes
* Bugfix cs3org/reva#1960: Return the updated share after updating
* Bugfix cs3org/reva#1965 cs3org/reva#1967: Fix the file target of user and group shares
* Bugfix cs3org/reva#1980: Propagate the etag after restoring a file version
* Enhancement cs3org/reva#1984: Replace OpenCensus with OpenTelemetry
* Bugfix cs3org/reva#1985: Add quota stubs
* Bugfix cs3org/reva#1987: Fix windows build
* Bugfix cs3org/reva#1990: Increase oc10 compatibility of owncloudsql
* Bugfix cs3org/reva#1992: Check if symlink exists instead of spamming the console
* Bugfix cs3org/reva#1993: fix owncloudsql GetMD


https://github.com/owncloud/ocis/pull/2423
