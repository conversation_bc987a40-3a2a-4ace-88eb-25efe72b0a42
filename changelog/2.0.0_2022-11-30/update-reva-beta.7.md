Enhancement: update reva to v2.8.0

Updated reva to version 2.8.0. This update includes:

* Bugfix [cs3org/reva#3158](https://github.com/cs3org/reva/pull/3158): Add name to the propfind response
* Bugfix [cs3org/reva#3157](https://github.com/cs3org/reva/pull/3157): Fix locking response codes
* Bugfix [cs3org/reva#3152](https://github.com/cs3org/reva/pull/3152): Disable caching of not found stat responses
* Bugfix [cs3org/reva#4251](https://github.com/cs3org/reva/pull/4251): Disable caching
* Enhancement [cs3org/reva#3154](https://github.com/cs3org/reva/pull/3154): Dataproviders now return file metadata
* Enhancement [cs3org/reva#3143](https://github.com/cs3org/reva/pull/3143): Add /app/open-with-web endpoint
* Enhancement [cs3org/reva#3156](https://github.com/cs3org/reva/pull/3156): Added language option to the app provider
* Enhancement [cs3org/reva#3148](https://github.com/cs3org/reva/pull/3148): Add new jsoncs3 share manager

https://github.com/owncloud/ocis/pull/4444
