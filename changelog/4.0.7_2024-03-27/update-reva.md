Bugfix: Update reva to include bugfixes and improvements

## Changelog for reva 2.13.4

*   Bugfix [cs3org/reva#4398](https://github.com/cs3org/reva/pull/4398): Fix ceph build
*   Bugfix [cs3org/reva#4396](https://github.com/cs3org/reva/pull/4396): Allow an empty credentials chain in the auth middleware
*   Bugfix [cs3org/reva#4423](https://github.com/cs3org/reva/pull/4423): Fix disconnected traces
*   Bugfix [cs3org/reva#4590](https://github.com/cs3org/reva/pull/4590): Fix uploading via a public link
*   Bugfix [cs3org/reva#4470](https://github.com/cs3org/reva/pull/4470): Keep failed processing status
*   Enhancement [cs3org/reva#4397](https://github.com/cs3org/reva/pull/4397): Introduce UploadSessionLister interface

https://github.com/owncloud/ocis/pull/8718
