Bugfix: update reva to version 2.12.0

Changelog for reva 2.12.0 (2022-11-25)                                                                                                                                                                                                                  2 ✘  14:57:56 
=======================================

*   Bugfix [cs3org/reva#3436](https://github.com/cs3org/reva/pull/3436): Allow updating to internal link
*   Bugfix [cs3org/reva#3473](https://github.com/cs3org/reva/pull/3473): Decomposedfs fix revision download
*   Bugfix [cs3org/reva#3482](https://github.com/cs3org/reva/pull/3482): Decomposedfs propagate sizediff
*   Bugfix [cs3org/reva#3449](https://github.com/cs3org/reva/pull/3449): Don't leak space information on update drive
*   Bugfix [cs3org/reva#3470](https://github.com/cs3org/reva/pull/3470): Add missing events for managing spaces
*   Bugfix [cs3org/reva#3472](https://github.com/cs3org/reva/pull/3472): Fix an oCDAV error message
*   Bugfix [cs3org/reva#3452](https://github.com/cs3org/reva/pull/3452): Fix access to spaces shared via public link
*   Bugfix [cs3org/reva#3440](https://github.com/cs3org/reva/pull/3440): Set proper names and paths for space roots
*   Bugfix [cs3org/reva#3437](https://github.com/cs3org/reva/pull/3437): Refactor delete error handling
*   Bugfix [cs3org/reva#3432](https://github.com/cs3org/reva/pull/3432): Remove share jail fix
*   Bugfix [cs3org/reva#3458](https://github.com/cs3org/reva/pull/3458): Set the Oc-Fileid header when copying items
*   Enhancement [cs3org/reva#3441](https://github.com/cs3org/reva/pull/3441): Cover ocdav with more unit tests
*   Enhancement [cs3org/reva#3493](https://github.com/cs3org/reva/pull/3493): Configurable filelock duration factor in decomposedfs
*   Enhancement [cs3org/reva#3397](https://github.com/cs3org/reva/pull/3397): Reduce lock contention issues

https://github.com/owncloud/ocis/pull/5092
https://github.com/owncloud/ocis/pull/5131
